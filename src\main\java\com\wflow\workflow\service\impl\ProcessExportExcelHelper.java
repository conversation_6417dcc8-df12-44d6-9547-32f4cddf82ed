package com.wflow.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wflow.workflow.bean.vo.ProcessExportVo;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Excel导出辅助类
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Component
public class ProcessExportExcelHelper {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 创建流程基本信息工作表
     */
    public void createProcessInfoSheet(Workbook workbook, ProcessExportVo.ProcessBasicInfo processInfo,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("流程基本信息");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "属性名称", "属性值" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 添加数据行
        addDataRow(sheet, rowNum++, "流程实例ID", processInfo.getInstanceId(), dataStyle);
        addDataRow(sheet, rowNum++, "流程名称", processInfo.getProcessDefName(), dataStyle);
        addDataRow(sheet, rowNum++, "实例名称", processInfo.getInstanceName(), dataStyle);
        addDataRow(sheet, rowNum++, "流程定义ID", processInfo.getProcessDefId(), dataStyle);
        addDataRow(sheet, rowNum++, "部署ID", processInfo.getDeployId(), dataStyle);
        addDataRow(sheet, rowNum++, "版本", String.valueOf(processInfo.getVersion()), dataStyle);
        addDataRow(sheet, rowNum++, "状态", processInfo.getStatus(), dataStyle);
        addDataRow(sheet, rowNum++, "结果", processInfo.getResult(), dataStyle);
        addDataRow(sheet, rowNum++, "发起人ID", processInfo.getStartUserId(), dataStyle);
        addDataRow(sheet, rowNum++, "发起人姓名", processInfo.getStartUserName(), dataStyle);
        addDataRow(sheet, rowNum++, "发起人部门", processInfo.getStartUserDept(), dataStyle);
        addDataRow(sheet, rowNum++, "发起时间",
                processInfo.getStartTime() != null ? processInfo.getStartTime().format(DATE_TIME_FORMATTER) : "",
                dataStyle);
        addDataRow(sheet, rowNum++, "结束时间",
                processInfo.getEndTime() != null ? processInfo.getEndTime().format(DATE_TIME_FORMATTER) : "",
                dataStyle);
        addDataRow(sheet, rowNum++, "描述", processInfo.getDescription(), dataStyle);

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建表单定义工作表
     */
    public void createFormDefinitionSheet(Workbook workbook, List<ProcessExportVo.FormFieldDefinition> definitions,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("表单字段定义");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "字段ID", "字段Key", "字段名称", "字段类型", "字段标签", "是否必填", "默认值", "选项", "验证规则", "描述" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 添加数据行
        if (CollectionUtil.isNotEmpty(definitions)) {
            for (ProcessExportVo.FormFieldDefinition definition : definitions) {
                Row dataRow = sheet.createRow(rowNum++);

                setCellValue(dataRow, 0, definition.getFieldId(), dataStyle);
                setCellValue(dataRow, 1, definition.getFieldKey(), dataStyle);
                setCellValue(dataRow, 2, definition.getFieldName(), dataStyle);
                setCellValue(dataRow, 3, definition.getFieldType(), dataStyle);
                setCellValue(dataRow, 4, definition.getFieldLabel(), dataStyle);
                setCellValue(dataRow, 5, definition.getRequired() != null ? (definition.getRequired() ? "是" : "否") : "",
                        dataStyle);
                setCellValue(dataRow, 6, definition.getDefaultValue(), dataStyle);
                setCellValue(dataRow, 7, definition.getOptions(), dataStyle);
                setCellValue(dataRow, 8, definition.getValidation(), dataStyle);
                setCellValue(dataRow, 9, definition.getDescription(), dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建表单数据工作表
     */
    public void createFormDataSheet(Workbook workbook, List<ProcessExportVo.FormDataInfo> formData,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("表单填写数据");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "字段ID", "字段Key", "字段名称", "填写值", "显示值", "创建时间", "更新时间" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 添加数据行
        if (CollectionUtil.isNotEmpty(formData)) {
            for (ProcessExportVo.FormDataInfo data : formData) {
                Row dataRow = sheet.createRow(rowNum++);

                setCellValue(dataRow, 0, data.getFieldId(), dataStyle);
                setCellValue(dataRow, 1, data.getFieldKey(), dataStyle);
                setCellValue(dataRow, 2, data.getFieldName(), dataStyle);
                setCellValue(dataRow, 3, data.getFieldValue(), dataStyle);
                setCellValue(dataRow, 4, data.getDisplayValue(), dataStyle);
                setCellValue(dataRow, 5,
                        data.getCreateTime() != null ? data.getCreateTime().format(DATE_TIME_FORMATTER) : "",
                        dataStyle);
                setCellValue(dataRow, 6,
                        data.getUpdateTime() != null ? data.getUpdateTime().format(DATE_TIME_FORMATTER) : "",
                        dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建审批记录工作表
     */
    public void createApprovalRecordSheet(Workbook workbook, List<ProcessExportVo.ApprovalRecord> records,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("审批记录");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "任务ID", "任务名称", "节点ID", "节点名称", "处理人ID", "处理人姓名", "处理人部门",
                "处理结果", "审批意见", "开始时间", "结束时间", "处理时长" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);

        }

        // 添加数据行
        if (CollectionUtil.isNotEmpty(records)) {
            for (ProcessExportVo.ApprovalRecord record : records) {
                Row dataRow = sheet.createRow(rowNum++);

                setCellValue(dataRow, 0, record.getTaskId(), dataStyle);
                setCellValue(dataRow, 1, record.getTaskName(), dataStyle);
                setCellValue(dataRow, 2, record.getNodeId(), dataStyle);
                setCellValue(dataRow, 3, record.getNodeName(), dataStyle);
                setCellValue(dataRow, 4, record.getAssignee(), dataStyle);
                setCellValue(dataRow, 5, record.getAssigneeName(), dataStyle);
                setCellValue(dataRow, 6, record.getAssigneeDept(), dataStyle);
                setCellValue(dataRow, 7, record.getResult(), dataStyle);
                setCellValue(dataRow, 8, record.getComment(), dataStyle);
                setCellValue(dataRow, 9,
                        record.getStartTime() != null ? record.getStartTime().format(DATE_TIME_FORMATTER) : "",
                        dataStyle);
                setCellValue(dataRow, 10,
                        record.getEndTime() != null ? record.getEndTime().format(DATE_TIME_FORMATTER) : "", dataStyle);
                setCellValue(dataRow, 11, record.getDuration(), dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建附件清单工作表
     */
    public void createAttachmentListSheet(Workbook workbook, List<ProcessExportVo.AttachmentInfo> attachments,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("附件清单");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "文件ID", "文件名", "原始文件名", "文件类型", "文件大小(字节)", "文件大小(MB)", "上传人", "上传时间" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 添加数据行
        if (CollectionUtil.isNotEmpty(attachments)) {
            for (ProcessExportVo.AttachmentInfo attachment : attachments) {
                Row dataRow = sheet.createRow(rowNum++);

                setCellValue(dataRow, 0, attachment.getFileId(), dataStyle);
                setCellValue(dataRow, 1, attachment.getFileName(), dataStyle);
                setCellValue(dataRow, 2, attachment.getOriginalName(), dataStyle);
                setCellValue(dataRow, 3, attachment.getFileType(), dataStyle);
                setCellValue(dataRow, 4,
                        attachment.getFileSize() != null ? String.valueOf(attachment.getFileSize()) : "", dataStyle);
                setCellValue(dataRow, 5,
                        attachment.getFileSize() != null
                                ? String.format("%.2f", attachment.getFileSize() / 1024.0 / 1024.0)
                                : "",
                        dataStyle);
                setCellValue(dataRow, 6, attachment.getUploadUser(), dataStyle);
                setCellValue(dataRow, 7,
                        attachment.getUploadTime() != null ? attachment.getUploadTime().format(DATE_TIME_FORMATTER)
                                : "",
                        dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 添加数据行（两列格式）
     */
    private void addDataRow(Sheet sheet, int rowNum, String label, String value, CellStyle dataStyle) {
        Row row = sheet.createRow(rowNum);

        Cell labelCell = row.createCell(0);
        labelCell.setCellValue(label);
        labelCell.setCellStyle(dataStyle);

        Cell valueCell = row.createCell(1);
        valueCell.setCellValue(StrUtil.isNotBlank(value) ? value : "");
        valueCell.setCellStyle(dataStyle);
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Row row, int columnIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(StrUtil.isNotBlank(value) ? value : "");
        cell.setCellStyle(cellStyle);
    }
}
