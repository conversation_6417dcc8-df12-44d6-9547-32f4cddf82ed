package com.wflow.workflow.bean.jumpback;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流程回跳执行结果
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JumpBackResult implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 执行是否成功
     */
    private Boolean success;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 配置ID
     */
    private String configId;

    /**
     * 源节点ID
     */
    private String sourceNodeId;

    /**
     * 目标节点ID
     */
    private String targetNodeId;

    /**
     * 当前回跳次数
     */
    private Integer jumpCount;

    /**
     * 回跳原因
     */
    private String jumpReason;

    /**
     * 回跳前的变量状态
     */
    private Map<String, Object> variablesBefore;

    /**
     * 回跳后的变量状态
     */
    private Map<String, Object> variablesAfter;

    /**
     * 执行状态
     */
    private ExecutionStatus executionStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 执行人
     */
    private String executeBy;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionDuration;

    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        SUCCESS("SUCCESS"),
        FAILED("FAILED"),
        PARTIAL_SUCCESS("PARTIAL_SUCCESS"),
        CANCELLED("CANCELLED"),
        TIMEOUT("TIMEOUT");

        private final String value;

        ExecutionStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static ExecutionStatus fromValue(String value) {
            for (ExecutionStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown execution status: " + value);
        }
    }

    /**
     * 创建成功结果
     */
    public static JumpBackResult success(String processInstanceId, String configId, 
                                       String sourceNodeId, String targetNodeId, 
                                       Integer jumpCount) {
        return JumpBackResult.builder()
                .success(true)
                .processInstanceId(processInstanceId)
                .configId(configId)
                .sourceNodeId(sourceNodeId)
                .targetNodeId(targetNodeId)
                .jumpCount(jumpCount)
                .executionStatus(ExecutionStatus.SUCCESS)
                .executeTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static JumpBackResult failure(String processInstanceId, String configId, 
                                       String errorMessage) {
        return JumpBackResult.builder()
                .success(false)
                .processInstanceId(processInstanceId)
                .configId(configId)
                .executionStatus(ExecutionStatus.FAILED)
                .errorMessage(errorMessage)
                .executeTime(LocalDateTime.now())
                .build();
    }
}
