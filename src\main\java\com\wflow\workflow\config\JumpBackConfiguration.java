package com.wflow.workflow.config;

import com.wflow.workflow.listener.JumpBackEventListener;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程回跳功能配置类
 * 配置事件监听器和相关组件
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Slf4j
@Configuration
public class JumpBackConfiguration {

    @Autowired
    private SpringProcessEngineConfiguration processEngineConfiguration;

    @Autowired
    private JumpBackEventListener jumpBackEventListener;

    /**
     * 配置流程回跳事件监听器
     */
    @PostConstruct
    public void configureJumpBackEventListeners() {
        log.info("配置流程回跳事件监听器");
        
        try {
            // 获取现有的事件监听器列表
            List<FlowableEventListener> eventListeners = processEngineConfiguration.getEventListeners();
            if (eventListeners == null) {
                eventListeners = new ArrayList<>();
            }

            // 添加回跳事件监听器
            eventListeners.add(jumpBackEventListener);
            
            // 设置回配置
            processEngineConfiguration.setEventListeners(eventListeners);
            
            log.info("流程回跳事件监听器配置完成");
            
        } catch (Exception e) {
            log.error("配置流程回跳事件监听器时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建回跳事件监听器Bean
     * 如果需要自定义配置，可以在这里进行
     */
    @Bean
    public JumpBackEventListener jumpBackEventListener() {
        log.info("创建流程回跳事件监听器Bean");
        return new JumpBackEventListener();
    }
}
