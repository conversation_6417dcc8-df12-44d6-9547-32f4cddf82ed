package com.wflow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.WflowFormStatisticsConfig;
import com.wflow.bean.vo.FormStatisticsVo;

import java.util.List;

/**
 * 表单统计服务接口
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public interface FormStatisticsService {

    /**
     * 创建统计配置
     * @param config 统计配置
     * @return 配置ID
     */
    String createStatisticsConfig(WflowFormStatisticsConfig config);

    /**
     * 更新统计配置
     * @param config 统计配置
     * @return 是否成功
     */
    boolean updateStatisticsConfig(WflowFormStatisticsConfig config);

    /**
     * 删除统计配置
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean deleteStatisticsConfig(String configId);

    /**
     * 启用/禁用统计配置
     * @param configId 配置ID
     * @param status 状态：0=禁用，1=启用
     * @return 是否成功
     */
    boolean toggleStatisticsConfig(String configId, Integer status);

    /**
     * 分页查询统计配置
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param formId 表单ID（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    Page<WflowFormStatisticsConfig> getStatisticsConfigs(Integer pageNo, Integer pageSize, 
                                                          String formId, Integer status);

    /**
     * 根据表单ID获取统计配置
     * @param formId 表单ID
     * @return 统计配置列表
     */
    List<WflowFormStatisticsConfig> getStatisticsConfigsByFormId(String formId);

    /**
     * 执行实时统计
     * @param configId 统计配置ID
     * @param includeFinished 是否包含已完成的流程
     * @return 统计结果
     */
    FormStatisticsVo executeRealTimeStatistics(String configId, boolean includeFinished);

    /**
     * 批量执行统计
     * @param configIds 统计配置ID列表
     * @param includeFinished 是否包含已完成的流程
     * @return 统计结果列表
     */
    List<FormStatisticsVo> executeBatchStatistics(List<String> configIds, boolean includeFinished);

    /**
     * 根据表单ID执行所有相关统计
     * @param formId 表单ID
     * @param includeFinished 是否包含已完成的流程
     * @return 统计结果列表
     */
    List<FormStatisticsVo> executeStatisticsByFormId(String formId, boolean includeFinished);

    /**
     * 获取表单字段的所有可能值（用于配置统计时的参考）
     * @param formId 表单ID
     * @param fieldId 字段ID
     * @param limit 限制返回数量
     * @return 字段值列表
     */
    List<String> getFieldDistinctValues(String formId, String fieldId, Integer limit);

    /**
     * 刷新统计缓存
     * @param configId 统计配置ID
     * @return 是否成功
     */
    boolean refreshStatisticsCache(String configId);
    List<WflowFormStatisticsConfig> getMenu();
}
