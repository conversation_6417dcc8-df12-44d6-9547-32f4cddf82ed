package com.wflow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.wflow.bean.entity.FileInfo;
import com.wflow.bean.enums.FileTypeEnum;
import com.wflow.service.FileManagerService;
import com.wflow.utils.FileWflowUtil;
import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件管理服务
 * <AUTHOR> willian fu
 * @date : 2022/9/7
 */
@RestController
@RequestMapping("wflow/res")
    @Tag(name = "文件管理服务")
public class    FileManagerController {

    @Autowired
    private FileManagerService fileManagerService;

    /**
     * 通过文件ID下载/获取文件/图片
     * 文件ID格式为 文件md5取中间16位 + 文件后缀
     * @param fileId 文件ID
     * @param name 下载的文件名
     * @param isSign 是否为签名，签名的话单独一个文件夹存
     * @return 文件流
     * @throws IOException
     */
    @GetMapping("{fileId}")
    @ApiOperationSupport(order = 1)
    @Operation(summary ="通过文件ID下载/获取文件/图片")
    public Object getFileById(@PathVariable String fileId,
                              @RequestParam(required = false) String name,
                              @RequestParam(defaultValue = "false") Boolean isSign) throws IOException {
        return R.resource(fileManagerService.getFileById(fileId, name, isSign), name);
    }

    /**
     * 上传文件/图片
     * @param file 文件流
     * @param isImg 是否为图片
     * @param isSign 是否为签名，签名的话单独一个文件夹存
     * @return 上传结果
     * @throws IOException
     */
    @PostMapping
    @ApiOperationSupport(order = 2)
    @Operation(summary ="上传文件/图片")
    public Object uploadFile(MultipartFile file,
                             @RequestParam(defaultValue = "false") Boolean isImg,
                             @RequestParam(defaultValue = "false") Boolean isSign) throws IOException {
        return R.ok(fileManagerService.uploadFile(file, isImg, isSign));
    }

    @Operation(summary = "多文件上传", description = "多文件上传")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "files", description = "文件流", required = true, ref = "file"),
            @Parameter(name = "fileType", description = "文件类型", required = true)
    })
    @PostMapping("/uploads")
    public Object uploadFiles(@RequestParam("files") MultipartFile[] files, FileTypeEnum fileType) {
        List<FileInfo> fileInfo = FileWflowUtil.uploadFiles(files, fileType);
        return R.ok(fileInfo);
    }

    /**
     * 通过文件ID删除文件
     * 文件ID格式为 文件md5取中间16位 + 文件后缀
     * @param fileId 文件ID
     * @return response
     */
    @DeleteMapping("{fileId}")
    @ApiOperationSupport(order = 4)
    @Operation(summary ="通过文件ID删除文件")
    public Object delFileById(@PathVariable String fileId,
                              @RequestParam(defaultValue = "false") Boolean isSign){
        fileManagerService.delFileById(fileId, isSign);
        return R.ok("操作成功");
    }

    @Operation(summary = "导入范围", description = "导入范围-支持zip/txt/cad")
    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "file", description = "文件流", required = true, ref = "file")
    })
    @PostMapping("/file2json")
    public Object shp2json(@RequestParam("file") MultipartFile file) throws Exception {
        String filename = file.getOriginalFilename();
        String suf = StrUtil.subSuf(filename, filename.lastIndexOf(".") + 1);
        FileTypeEnum fileTypeEnum = FileTypeEnum.ofSuf(suf);
        switch (fileTypeEnum) {
            case PACKAGE:
                return fileManagerService.shpZip2GeoJson(file);
            case TXT:
                return fileManagerService.txt2GeoJsonByGuo(file);
            case EXCEL:
                 return fileManagerService.excel2GeoJson(file);
            case SHP:
                return fileManagerService.shp2GeoJson(file);
            default:
                return R.ok("操作成功");
        }
    }
}
