package com.wflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wflow.bean.entity.*;
import com.wflow.bean.vo.FormlistVo;
import com.wflow.bean.vo.ModelGroupVo;
import com.wflow.bean.vo.WflowFormListVo;
import com.wflow.bean.vo.WflowModelDetailVo;
import com.wflow.exception.BusinessException;
import com.wflow.mapper.*;
import com.wflow.service.ModelGroupService;
import com.wflow.service.OrgRepositoryService;
import com.wflow.service.WflowProcessPlanService;
import com.wflow.utils.FormVersionUtil;
import com.wflow.utils.R;
import com.wflow.utils.UserUtil;
import com.wflow.workflow.bean.process.ProcessNode;
import com.wflow.workflow.bean.process.form.Form;
import com.wflow.workflow.bean.process.props.RootProps;
import com.wflow.workflow.service.FormService;
import com.wflow.workflow.service.ProcessModelService;
import com.wflow.workflow.service.ProcessNodeCatchService;
import jakarta.annotation.Resource;
import liquibase.pro.packaged.S;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> willian fu
 * @date : 2022/7/4
 */
@Slf4j
@Service
@Transactional
public class FormGroupServiceImpl implements ModelGroupService {

    @Autowired
    private WflowModelGroupsMapper groupsMapper;

    @Autowired
    private WflowModelsMapper modelsMapper;

    @Autowired
    private ProcessModelService modelService;

    @Autowired
    private ProcessNodeCatchService nodeCatchService;

    @Autowired
    private FormService formService;

    @Autowired
    private OrgRepositoryService orgRepositoryService;

    @Autowired
    private WflowProcessPlanMapper processPlanMapper;

    @Autowired
    private WflowModelGroupMapper  modelGroupMapper;

    @Autowired
    private WflowNodeFormInfoMapper wflowNodeFormInfoMapper;

    @Autowired
    private WflowFormInfoMapper formInfoMapper;

    @Autowired
    private WflowFormInfoHistorysMapper formInfoHistorysMapper;

    @Autowired
    private WflowFormDirMapper formDirMapper;


    @Resource
    private WflowModelHistorysMapper wflowModelHistorysMapper;

    @Resource
    private WflowModelPermsMapper modelPermsMapper;

    @Resource
    private WflowConfigurationfMapper configurationMapper;



    @Override
    public List<ModelGroupVo> getGroupModels(String userId, String modelName,String modelType) {
        // 初始化一个空的ModelGroupVo列表，用于存储分组后的模型数据
        List<ModelGroupVo> modelGroupVos = new LinkedList<>();
        //一次性查出所有表单，再分组归类
        Map<Long, List<ModelGroupVo.Form>> groupMap = new LinkedHashMap<>();
        List<ModelGroupVo.Form> models = Objects.nonNull(userId) ?
                orgRepositoryService.getModelsByPerm(userId):
                modelsMapper.getSysModels();
        models.forEach(m -> {
            List<ModelGroupVo.Form> forms = groupMap.get(m.getGroupId());
            if (Objects.isNull(forms)){
                forms = new ArrayList<>();
                if(StringUtils.isNotBlank(modelType) && m.getModelType().equals(modelType)){
                    if(modelName != null){
                        if(m.getFormName().contains(modelName)){
                            groupMap.put(m.getGroupId(), forms);
                        }
                    }else {
                        groupMap.put(m.getGroupId(), forms);
                    }
                }else {
                    groupMap.put(m.getGroupId(), forms);
                }
            }
            forms.add(m);
        });
        groupsMapper.selectList(new LambdaQueryWrapper<WflowModelGroups>()
                .orderByAsc(WflowModelGroups::getSort)).forEach(group -> {
            ModelGroupVo modelGroupVo = ModelGroupVo.builder()
                    .id(group.getGroupId())
                    .name(group.getGroupName())
                    .items(new LinkedList<>())
                    .build();
            modelGroupVos.add(modelGroupVo);
            modelGroupVo.setItems(groupMap.getOrDefault(group.getGroupId(), Collections.emptyList()));
        });
        return modelGroupVos;
    }

    @Override
    public Object getGroupModels2(Long platformAppConfId, String modelName) {
        String userId = UserUtil.getLoginUserId();
        List<ModelGroupVo> modelGroupVos = new LinkedList<>();
        //查询应用下拥有的分组
        List<WflowProcessPlan> plans = this.processPlanMapper.selectList(
                new LambdaQueryWrapper<WflowProcessPlan>()
                        .eq(WflowProcessPlan::getPlatformAppConfId, platformAppConfId));
        if(plans.isEmpty()){
            return modelGroupVos;
        }
        List<Long> groupIds = plans.stream().map(WflowProcessPlan::getGroupId).collect(Collectors.toList());
        List<WflowModelGroups> wflowModelGroups = this.groupsMapper.selectList(new LambdaQueryWrapper<WflowModelGroups>()
                .in(WflowModelGroups::getGroupId, groupIds)
                .orderByAsc(WflowModelGroups::getSort));
        wflowModelGroups.forEach(group -> {
            ModelGroupVo modelGroupVo = new ModelGroupVo();
            modelGroupVo.setId(group.getGroupId());
            modelGroupVo.setName(group.getGroupName());
            List<ModelGroupVo.Form> items = new ArrayList<>();
//            List<WflowModels> models = this.modelsMapper.selectList(new LambdaQueryWrapper<WflowModels>()
//                    .eq(WflowModels::getGroupId,group.getGroupId())
//                    .eq(WflowModels::getIsDelete,false).eq(WflowModels::getIsStop,false)
//                    .orderByAsc(WflowModels::getSort));

            List<ModelGroupVo.Form> models =  orgRepositoryService.getModelsByPerm(userId);

            modelPermsMapper.selectList(Wrappers.<WflowModelPerms>lambdaQuery()
                    .eq(WflowModelPerms::getOrgId, userId)
            );
            //把models中的数据都拷贝到items中
            models.forEach(model -> {
                if(StringUtils.isNotBlank(model.getProcessDefId())){
                    if(group.getGroupId().equals(model.getGroupId())){
                        items.add(model);
                    }
                }
            });
            modelGroupVo.setItems(items);
            modelGroupVos.add(modelGroupVo);
        });
        return modelGroupVos;
    }

    @Override
    public Object getNodeModelById(String formId) {
        WflowModels wflowModels = modelsMapper.selectById(formId);
        if(Objects.isNull(wflowModels)){
            return null;
        }
        ProcessNode<?> root = nodeCatchService.reloadProcessByStr(wflowModels.getProcess()).get("root");
        boolean flag = FormVersionUtil.getFormVersion(wflowModels.getFormItems());
        List<Form> forms;
        List<WflowFormDir> dirList = Collections.emptyList();

        if(!flag){
            String dirData = configurationMapper.selectOne(Wrappers.<WflowConfiguration>lambdaQuery()
                    .select(WflowConfiguration::getDirData)
                    .eq(WflowConfiguration::getModelId, formId)).toString();
            if(StringUtils.isNotBlank(dirData)){
                dirList = JSONArray.parseArray(dirData,WflowFormDir.class);
            }else {
                dirList  = getTree(wflowModels.getFormId(),null);
            }
            forms = formService.filterFormByPermConfigForNode(JSONArray.parseArray(wflowModels.getFormItems(), Form.class), null,root);
        }else {
            forms = formService.filterFormByPermConfigForRoot(JSONArray.parseArray(wflowModels.getFormItems(), Form.class), (RootProps) root.getProps());
        }
        return WflowModelDetailVo.builder()
                .formId(formId).formItems(forms)
                .formName(wflowModels.getFormName())
                .logo(wflowModels.getLogo())
                .formConfig(JSONObject.parseObject(wflowModels.getFormConfig()))
                .processDefId(wflowModels.getProcessDefId())
                .process(root)
                .formDirs(dirList)
                .build();
    }

    @Override
    public Object getFormItem(String formId, String dirId,Integer version, String nodeId) {
        ProcessNode<?> root;
        if(StringUtils.isNotBlank(nodeId)) {
            WflowModelHistorys modelHistorys = wflowModelHistorysMapper.selectOne(
                    Wrappers.<WflowModelHistorys>lambdaQuery()
                            .eq(WflowModelHistorys::getFormId, formId)
                            .eq(WflowModelHistorys::getVersion, version)
            );
            root = nodeCatchService.reloadProcessByStr(modelHistorys.getProcess()).get(nodeId);
        }else {
            WflowModels wflowModels = modelsMapper.selectById(formId);
            root = nodeCatchService.reloadProcessByStr(wflowModels.getProcess()).get("root");
        }
        List<WflowFormInfoHistorys> formInfos = getFormInfo(formId,dirId,null);
        return formInfos.stream().map(
                map->{

                    List<Form> forms = formService.filterFormByPermConfigForNode(JSONArray.parseArray(map.getFormItems(), Form.class),null, root);
                    return WflowFormListVo.builder()
                            .formId(map.getId()).formItems(forms)
                            .formName(map.getMaterialName())
                            .formConfig(JSONObject.parseObject(map.getFormConfig()))
                            .dirId(dirId)
                            .build();
                }
        ).collect(Collectors.toList());
    }

    @Override
    public void modelGroupsSort(List<Long> groups) {
        List<Long> list = groups.stream().filter(id -> !id.equals(0L) && !id.equals(1L)).collect(Collectors.toList());
        for (int i = 0; i < list.size(); i++) {
            groupsMapper.updateById(WflowModelGroups.builder().groupId(list.get(i)).sort(i).build());
        }
    }

    @Override
    public Object getModelById(String formId) {
        WflowModels wflowModels = modelsMapper.selectById(formId);
        ProcessNode<?> root = nodeCatchService.reloadProcessByStr(wflowModels.getProcess()).get("root");
        List<Form> forms = formService.filterFormByPermConfigForRoot(JSONArray.parseArray(wflowModels.getFormItems(), Form.class), (RootProps) root.getProps());
        return WflowModelDetailVo.builder()
                .formId(formId).formItems(forms)
                .formName(wflowModels.getFormName())
                .logo(wflowModels.getLogo())
                .formConfig(JSONObject.parseObject(wflowModels.getFormConfig()))
                .processDefId(wflowModels.getProcessDefId())
                .process(root)
                .build();
    }

    @Override
    public Object getModelByDefId(String defId) {
        WflowModels models = modelsMapper.selectOne(new LambdaQueryWrapper<WflowModels>()
                .eq(WflowModels::getProcessDefId, defId));
        if (Objects.isNull(models)) {
            throw new BusinessException("该流程已被重新发布，原有版本失效");
        }
        return models;
    }

    @Override
    public void updateModelGroupName(Long id, String name) {
        groupsMapper.updateById(WflowModelGroups.builder().groupId(id).groupName(name).build());
    }

    @Override
    public void createModelGroup(String name) {
        groupsMapper.insert(WflowModelGroups.builder().sort(0).groupName(name).updated(new Date()).build());
    }

    @Override
    @Transactional
    public void deleteModelGroup(Long id) {
        //先转移，再删除分组
        if (Objects.nonNull(id) && id > 1) {
            Set<String> collect = modelsMapper.selectList(new LambdaQueryWrapper<WflowModels>()
                            .select(WflowModels::getFormId)
                            .eq(WflowModels::getGroupId, id)).stream()
                    .map(WflowModels::getFormId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(collect)) {
                //移动到其他分组 id = 1
                modelsMapper.update(WflowModels.builder().groupId(1L).build(),
                        new LambdaQueryWrapper<WflowModels>()
                                .in(WflowModels::getFormId, collect));
            }
            groupsMapper.deleteById(id);
            //删除分组时顺便删除该分组和子系统的关联关系
            processPlanMapper.delete(Wrappers.<WflowProcessPlan>lambdaQuery().eq(WflowProcessPlan::getGroupId, id));
        } else {
            throw new BusinessException("系统默认分组不允许删除");
        }
    }

    @Override
    public void groupModelSort(Long groupId, List<String> modelIds) {
        WflowModels wflowModels = WflowModels.builder().groupId(groupId).build();
        for (int i = 0; i < modelIds.size(); i++) {
            wflowModels.setFormId(modelIds.get(i));
            wflowModels.setSort(i);
            modelsMapper.updateById(wflowModels);
        }
    }

    @Override
    @Transactional
    public void deleteModel(String modelId) {
        modelService.delProcess(modelId);
        modelsMapper.updateById(WflowModels.builder().formId(modelId).isDelete(true).build());
    }

    @Override
    public void modelMoveToGroup(String modelId, Long groupId) {
        modelsMapper.updateById(WflowModels.builder().formId(modelId).groupId(groupId).sort(0).build());
    }

    @Override
    public void enOrDisModel(String modelId, Boolean active) {
        modelsMapper.updateById(WflowModels.builder().formId(modelId)
//                .groupId(Boolean.TRUE.equals(active) ? 0 : 1L)
                .isStop(Boolean.TRUE.equals(active)).build());
    }

    @Override
    public List<WflowModels> getModelItem() {
        return modelsMapper.selectList(new LambdaQueryWrapper<WflowModels>()
                .select(WflowModels::getFormId, WflowModels::getFormName)
                .eq(WflowModels::getIsStop, false));
    }


    @Override
    public List<WflowModelGroups> getModelGroups() {
        return groupsMapper.selectList(new LambdaQueryWrapper<WflowModelGroups>()
//                .notIn(WflowModelGroups::getGroupId, CollectionUtil.newArrayList(0))
                .orderByAsc(WflowModelGroups::getSort));
    }
    private List<WflowFormInfoHistorys> getFormInfo(String formId,String dirId,String defId) {
        // 查询关联的 formInfoId
        List<WflowNodeFormInfo> wflowNodeFormInfoList = wflowNodeFormInfoMapper.selectList(
                Wrappers.<WflowNodeFormInfo>lambdaQuery()
                        .select(WflowNodeFormInfo::getFormInfoId)
                        .eq(StringUtils.isNotBlank(formId),WflowNodeFormInfo::getModelHistorysId, formId)
                        .eq(StringUtils.isNotBlank(defId),WflowNodeFormInfo::getProcessDefId, defId)
        );

        // 提取 formInfoId 的值集合
        List<String> formInfoIds = wflowNodeFormInfoList.stream()
                .map(WflowNodeFormInfo::getFormInfoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 根据 formInfoIds 查询表单信息
        List<WflowFormInfoHistorys> formInfos = new ArrayList<>();
        if (!formInfoIds.isEmpty()) {
            formInfos = formInfoHistorysMapper.selectList(
                    Wrappers.<WflowFormInfoHistorys>lambdaQuery()
                            .in(WflowFormInfoHistorys::getId, formInfoIds)
                            .eq(StringUtils.isNotBlank(dirId),WflowFormInfoHistorys::getFormDirId, dirId)
            );
        } else {
            log.warn("未找到关联的表单ID, formId={}", formId);
        }
        return formInfos;
    }

    public List<WflowFormDir> getParentTree(List<String> ids) {
        List<WflowFormDir> nodes = formDirMapper.selectParentTreeByIds(ids);
        return buildTree(nodes);
    }

    private List<WflowFormDir> buildTree(List<WflowFormDir> nodes) {
        Map<String, WflowFormDir> nodeMap = new LinkedHashMap<>();
        List<WflowFormDir> roots = new ArrayList<>();

        // 按层级降序排序
        nodes.sort(Comparator.comparingInt(WflowFormDir::getLevel).reversed());

        for (WflowFormDir node : nodes) {
            // 节点去重
            nodeMap.putIfAbsent(node.getId(), node);

            // 挂载到父节点
            if (!"0".equals(node.getParentId())) {
                WflowFormDir parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }

        // 提取根节点
        nodeMap.values().stream()
                .filter(n -> "0".equals(n.getParentId()))
                .forEach(roots::add);

        // 子节点排序
        roots.forEach(this::sortChildren);
        return roots;
    }

    private void sortChildren(WflowFormDir node) {
        node.getChildren().sort(Comparator.comparingInt(WflowFormDir::getShowSort));
        node.getChildren().forEach(this::sortChildren);
    }
    public List<WflowFormDir> getTree(String formId,String defId) {
        List<WflowFormInfoHistorys> formInfos = getFormInfo(formId,null,defId);
        //处理数据
        List<String> id = formInfos.stream().map(WflowFormInfoHistorys::getFormDirId).distinct().collect(Collectors.toList());
        List<WflowFormDir> dirList = getParentTree(id);
        return dirList;
    }
}
