package com.wflow.workflow;

import com.wflow.workflow.bean.jumpback.JumpBackConfig;
import com.wflow.workflow.bean.jumpback.JumpBackResult;
import com.wflow.workflow.service.JumpBackConfigService;
import com.wflow.workflow.service.JumpBackExecutionService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 流程回跳功能测试类
 * 用于验证回跳功能的基本编译和运行
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@SpringBootTest
@SpringJUnitConfig
public class JumpBackFunctionalityTest {

    /**
     * 测试回跳配置创建
     */
    @Test
    public void testJumpBackConfigCreation() {
        // 创建回跳配置
        JumpBackConfig config = JumpBackConfig.builder()
                .processDefId("test-process-def")
                .sourceNodeId("gateway1")
                .targetNodeId("userTask1")
                .nodeType(JumpBackConfig.NodeType.EXCLUSIVE_GATEWAY)
                .jumpCondition(JumpBackConfig.JumpBackCondition.ALL_BRANCHES_COMPLETED)
                .maxJumpCount(3)
                .enabled(true)
                .build();

        // 验证配置创建成功
        assertNotNull(config);
        assertEquals("test-process-def", config.getProcessDefId());
        assertEquals("gateway1", config.getSourceNodeId());
        assertEquals("userTask1", config.getTargetNodeId());
        assertEquals(JumpBackConfig.NodeType.EXCLUSIVE_GATEWAY, config.getNodeType());
        assertEquals(JumpBackConfig.JumpBackCondition.ALL_BRANCHES_COMPLETED, config.getJumpCondition());
        assertEquals(3, config.getMaxJumpCount());
        assertTrue(config.getEnabled());
    }

    /**
     * 测试回跳结果创建
     */
    @Test
    public void testJumpBackResultCreation() {
        // 创建成功结果
        JumpBackResult successResult = JumpBackResult.success(
                "process-instance-1", 
                "config-1", 
                "gateway1", 
                "userTask1", 
                1);

        assertNotNull(successResult);
        assertTrue(successResult.getSuccess());
        assertEquals("process-instance-1", successResult.getProcessInstanceId());
        assertEquals("config-1", successResult.getConfigId());
        assertEquals("gateway1", successResult.getSourceNodeId());
        assertEquals("userTask1", successResult.getTargetNodeId());
        assertEquals(1, successResult.getJumpCount());
        assertEquals(JumpBackResult.ExecutionStatus.SUCCESS, successResult.getExecutionStatus());

        // 创建失败结果
        JumpBackResult failureResult = JumpBackResult.failure(
                "process-instance-1", 
                "config-1", 
                "测试错误信息");

        assertNotNull(failureResult);
        assertFalse(failureResult.getSuccess());
        assertEquals("process-instance-1", failureResult.getProcessInstanceId());
        assertEquals("config-1", failureResult.getConfigId());
        assertEquals("测试错误信息", failureResult.getErrorMessage());
        assertEquals(JumpBackResult.ExecutionStatus.FAILED, failureResult.getExecutionStatus());
    }

    /**
     * 测试数据映射配置
     */
    @Test
    public void testDataMappingConfiguration() {
        JumpBackConfig.JumpBackDataMapping dataMapping = JumpBackConfig.JumpBackDataMapping.builder()
                .keepHistoryVariables(true)
                .build();

        assertNotNull(dataMapping);
        assertTrue(dataMapping.getKeepHistoryVariables());
    }

    /**
     * 测试节点类型枚举
     */
    @Test
    public void testNodeTypeEnum() {
        // 测试从值创建枚举
        JumpBackConfig.NodeType exclusiveGateway = JumpBackConfig.NodeType.fromValue("exclusiveGateway");
        assertEquals(JumpBackConfig.NodeType.EXCLUSIVE_GATEWAY, exclusiveGateway);

        JumpBackConfig.NodeType parallelGateway = JumpBackConfig.NodeType.fromValue("parallelGateway");
        assertEquals(JumpBackConfig.NodeType.PARALLEL_GATEWAY, parallelGateway);

        JumpBackConfig.NodeType userTask = JumpBackConfig.NodeType.fromValue("userTask");
        assertEquals(JumpBackConfig.NodeType.USER_TASK, userTask);

        // 测试无效值
        assertThrows(IllegalArgumentException.class, () -> {
            JumpBackConfig.NodeType.fromValue("invalidType");
        });
    }

    /**
     * 测试回跳条件枚举
     */
    @Test
    public void testJumpBackConditionEnum() {
        // 测试从值创建枚举
        JumpBackConfig.JumpBackCondition allBranches = JumpBackConfig.JumpBackCondition.fromValue("all_branches_completed");
        assertEquals(JumpBackConfig.JumpBackCondition.ALL_BRANCHES_COMPLETED, allBranches);

        JumpBackConfig.JumpBackCondition anyBranch = JumpBackConfig.JumpBackCondition.fromValue("any_branch_completed");
        assertEquals(JumpBackConfig.JumpBackCondition.ANY_BRANCH_COMPLETED, anyBranch);

        JumpBackConfig.JumpBackCondition manual = JumpBackConfig.JumpBackCondition.fromValue("manual_trigger");
        assertEquals(JumpBackConfig.JumpBackCondition.MANUAL_TRIGGER, manual);

        // 测试无效值
        assertThrows(IllegalArgumentException.class, () -> {
            JumpBackConfig.JumpBackCondition.fromValue("invalidCondition");
        });
    }

    /**
     * 测试执行状态枚举
     */
    @Test
    public void testExecutionStatusEnum() {
        // 测试从值创建枚举
        JumpBackResult.ExecutionStatus success = JumpBackResult.ExecutionStatus.fromValue("SUCCESS");
        assertEquals(JumpBackResult.ExecutionStatus.SUCCESS, success);

        JumpBackResult.ExecutionStatus failed = JumpBackResult.ExecutionStatus.fromValue("FAILED");
        assertEquals(JumpBackResult.ExecutionStatus.FAILED, failed);

        JumpBackResult.ExecutionStatus cancelled = JumpBackResult.ExecutionStatus.fromValue("CANCELLED");
        assertEquals(JumpBackResult.ExecutionStatus.CANCELLED, cancelled);

        // 测试无效值
        assertThrows(IllegalArgumentException.class, () -> {
            JumpBackResult.ExecutionStatus.fromValue("INVALID_STATUS");
        });
    }
}
