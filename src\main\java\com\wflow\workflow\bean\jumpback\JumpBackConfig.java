package com.wflow.workflow.bean.jumpback;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 流程回跳配置实体
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JumpBackConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    private String id;

    /**
     * 流程定义ID
     */
    private String processDefId;

    /**
     * 流程定义Key
     */
    private String processDefKey;

    /**
     * 源节点ID（分支节点）
     */
    private String sourceNodeId;

    /**
     * 目标节点ID（回跳目标）
     */
    private String targetNodeId;

    /**
     * 节点类型
     */
    private NodeType nodeType;

    /**
     * 回跳条件
     */
    private JumpBackCondition jumpCondition;

    /**
     * 最大回跳次数
     */
    private Integer maxJumpCount;

    /**
     * 数据映射配置
     */
    private JumpBackDataMapping dataMapping;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 节点类型枚举
     */
    public enum NodeType {
        EXCLUSIVE_GATEWAY("exclusiveGateway"),
        PARALLEL_GATEWAY("parallelGateway"),
        INCLUSIVE_GATEWAY("inclusiveGateway"),
        USER_TASK("userTask"),
        SERVICE_TASK("serviceTask");

        private final String value;

        NodeType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static NodeType fromValue(String value) {
            for (NodeType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown node type: " + value);
        }
    }

    /**
     * 回跳条件枚举
     */
    public enum JumpBackCondition {
        ALL_BRANCHES_COMPLETED("all_branches_completed"),
        ANY_BRANCH_COMPLETED("any_branch_completed"),
        SPECIFIC_BRANCH_COMPLETED("specific_branch_completed"),
        CUSTOM_EXPRESSION("custom_expression"),
        MANUAL_TRIGGER("manual_trigger");

        private final String value;

        JumpBackCondition(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static JumpBackCondition fromValue(String value) {
            for (JumpBackCondition condition : values()) {
                if (condition.value.equals(value)) {
                    return condition;
                }
            }
            throw new IllegalArgumentException("Unknown jump back condition: " + value);
        }
    }

    /**
     * 数据映射配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JumpBackDataMapping implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 需要复制的变量列表
         */
        private List<String> copyVariables;

        /**
         * 需要清除的变量列表
         */
        private List<String> clearVariables;

        /**
         * 需要设置的变量映射
         */
        private Map<String, Object> setVariables;

        /**
         * 变量转换表达式
         */
        private String transformExpression;

        /**
         * 是否保留历史变量
         */
        private Boolean keepHistoryVariables;
    }
}
