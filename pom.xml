<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  
  <groupId>org.example</groupId>
  <artifactId>wflow-server</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>jar</packaging>
  
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.3.2</version>
    <relativePath/>
  </parent>
  
  <properties>

    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <java.version>17</java.version>
    <!--项目编码-->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <flowable.version>7.0.0</flowable.version>
  </properties>
  
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.20</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2 -->
    <dependency>
      <groupId>com.alibaba.fastjson2</groupId>
      <artifactId>fastjson2</artifactId>
      <version>2.0.40</version>
    </dependency>

    <dependency>
      <groupId>org.openjdk.nashorn</groupId>
      <artifactId>nashorn-core</artifactId>
      <version>15.4</version>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
      <version>3.5.7</version>
    </dependency>
    
    <!-- MySQL 连接驱动依赖 -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.28</version>
    </dependency>
    <dependency>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-core</artifactId>
    <version>1.40.0</version>
  </dependency>
    <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-spring-boot3-starter</artifactId>
      <version>1.40.0</version>
    </dependency>
  
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  
    <!-- spring mail -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>

    <!-- java工具类 -->
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.3.5</version>
    </dependency>

    <!-- easypoi文档工具类 -->
    <dependency>
      <groupId>cn.afterturn</groupId>
      <artifactId>easypoi-spring-boot-starter</artifactId>
      <version>4.5.0</version>
    </dependency>

    <!-- Apache POI - Excel文件处理 -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>4.1.2</version>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>4.1.2</version>
    </dependency>

    <!-- 如果项目中还没有以下依赖，也需要添加 -->

    <!-- FastJSON2 -->
    <dependency>
      <groupId>com.alibaba.fastjson2</groupId>
      <artifactId>fastjson2</artifactId>
      <version>2.0.52</version>
    </dependency>

    <dependency>
      <groupId>com.belerweb</groupId>
      <artifactId>pinyin4j</artifactId>
      <version>2.5.1</version>
    </dependency>

    <dependency>
      <groupId>org.flowable</groupId>
      <artifactId>flowable-spring-boot-starter-actuator</artifactId>
      <version>${flowable.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.googlecode.aviator</groupId>
      <artifactId>aviator</artifactId>
      <version>5.3.1</version>
    </dependency>
    
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
    
    <!--js沙箱环境，资源保护-->
    <dependency>
      <groupId>org.javadelight</groupId>
      <artifactId>delight-nashorn-sandbox</artifactId>
      <version>0.3.2</version>
    </dependency>
    


    <dependency>
      <groupId>org.liquibase</groupId>
      <artifactId>liquibase-core</artifactId>
      <version>4.6.1</version>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>0.12.3</version>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <version>0.12.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>0.12.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
      <version>4.3.0</version>
    </dependency>
<!--    &lt;!&ndash; SpringDoc OpenAPI 核心 &ndash;&gt;-->
<!--    <dependency>-->
<!--      <groupId>org.springdoc</groupId>-->
<!--      <artifactId>springdoc-openapi-ui</artifactId>-->
<!--      &lt;!&ndash;保持版本与Knife4j v4.0的版本一致，避免jar包冲突，因为Knife4j-v4.0.0版本依赖的springdoc版本是1.6.9 &ndash;&gt;-->
<!--      <version>1.6.9</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>org.json</groupId>
      <artifactId>json</artifactId>
      <version>20210307</version>
    </dependency>
    <!--    shp文件解析 开始    -->
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-shapefile</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-jdbc</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.ejml</groupId>
      <artifactId>ejml-ddense</artifactId>
      <version>0.39</version>
    </dependency>
    <dependency>
      <groupId>org.ejml</groupId>
      <artifactId>ejml-core</artifactId>
      <version>0.39</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-opengis</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-data</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-api</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-main</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-metadata</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-referencing</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-geojson</artifactId>
      <version>19.2</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.vividsolutions</groupId>
      <artifactId>jts</artifactId>
      <version>1.13</version>
    </dependency>
    <dependency>
      <groupId>org.gavaghan</groupId>
      <artifactId>geodesy</artifactId>
      <version>1.1.3</version>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-epsg-hsql</artifactId>
      <version>19.2</version>
    </dependency>
    <!--    shp文件解析 结束    -->

  </dependencies>
  
<!--  <build>-->
<!--    <plugins>-->
<!--      <plugin>-->
<!--        <groupId>org.springframework.boot</groupId>-->
<!--        <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--        <configuration>-->
<!--        </configuration>-->
<!--      </plugin>-->

<!--&lt;!&ndash;      <plugin>&ndash;&gt;-->
<!--&lt;!&ndash;        <groupId>org.apache.maven.plugins</groupId>&ndash;&gt;-->
<!--&lt;!&ndash;        <artifactId>maven-surefire-plugin</artifactId>&ndash;&gt;-->
<!--&lt;!&ndash;        <configuration>&ndash;&gt;-->
<!--&lt;!&ndash;          <testFailureIgnore>true</testFailureIgnore>&ndash;&gt;-->
<!--&lt;!&ndash;        </configuration>&ndash;&gt;-->
<!--&lt;!&ndash;      </plugin>&ndash;&gt;-->
<!--    </plugins>-->
<!--  </build>-->

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <!-- 指定该Main Class为全局的唯一入口 -->
          <mainClass>com.wflow.AppStater</mainClass>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>dev</id>
      <activation>
        <!-- 激活条件为"dev"系统属性存在 -->
        <property>
          <name>env</name>
          <value>dev</value>
        </property>
        <!-- 默认激活 -->
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>prod</id>
      <activation>
        <!-- 激活条件为"prod"环境变量存在 -->
        <property>
          <name>env</name>
          <value>prod</value>
        </property>
      </activation>
      <dependencies>
        <dependency>
          <groupId>com.github.xiaoymin</groupId>
          <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
          <version>4.3.0</version>
          <exclusions>
            <exclusion>
              <groupId>com.github.xiaoymin</groupId>
              <artifactId>knife4j-openapi3-ui</artifactId>
            </exclusion>
            <exclusion>
              <groupId>org.webjars</groupId>
              <artifactId>swagger-ui</artifactId>
            </exclusion>
          </exclusions>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
  <distributionManagement>
    <!-- Release仓库 -->
    <repository>
      <id>releases</id>
      <url>http://192.168.3.36/repository/maven-releases/</url>
    </repository>
    <!-- Snapshot仓库 -->
    <snapshotRepository>
      <id>snapshots</id>
      <url>http://192.168.3.36/repository/maven-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
</project>
