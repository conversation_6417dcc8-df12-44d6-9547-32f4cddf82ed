package com.wflow.workflow.service;

import com.wflow.workflow.bean.jumpback.JumpBackConfig;

import java.util.List;

/**
 * 流程回跳配置服务接口
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
public interface JumpBackConfigService {

    /**
     * 解析流程定义中的回跳配置
     * @param processDefinitionId 流程定义ID
     * @return 回跳配置列表
     */
    List<JumpBackConfig> parseJumpBackConfigs(String processDefinitionId);

    /**
     * 保存回跳配置
     * @param config 回跳配置
     * @return 配置ID
     */
    String saveJumpBackConfig(JumpBackConfig config);

    /**
     * 获取节点的回跳配置
     * @param processDefId 流程定义ID
     * @param nodeId 节点ID
     * @return 回跳配置
     */
    JumpBackConfig getJumpBackConfig(String processDefId, String nodeId);

    /**
     * 根据流程定义ID获取所有回跳配置
     * @param processDefId 流程定义ID
     * @return 回跳配置列表
     */
    List<JumpBackConfig> getJumpBackConfigsByProcessDef(String processDefId);

    /**
     * 更新回跳配置
     * @param config 回跳配置
     * @return 是否成功
     */
    boolean updateJumpBackConfig(JumpBackConfig config);

    /**
     * 删除回跳配置
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean deleteJumpBackConfig(String configId);

    /**
     * 启用/禁用回跳配置
     * @param configId 配置ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean toggleJumpBackConfig(String configId, boolean enabled);

    /**
     * 验证回跳配置的有效性
     * @param config 回跳配置
     * @return 验证结果
     */
    ValidationResult validateJumpBackConfig(JumpBackConfig config);

    /**
     * 验证结果类
     */
    class ValidationResult {
        private boolean valid;
        private String errorMessage;

        public ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }

        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
