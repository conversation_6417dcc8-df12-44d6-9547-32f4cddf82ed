package com.wflow.utils;

import cn.hutool.core.util.StrUtil;
import com.wflow.bean.entity.FileInfo;
import com.wflow.bean.enums.FileTypeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 */
//@Component
@Slf4j
public class FileWflowUtil {

    /**
     * 本地路径
     */
    private static String LOCAL_URL;
    
    /**
     * 服务器路径
     */
    private static String SERVER_URL;

    /**
     * 文件上传路径
     */
    @Getter
    private static String FILE_UPLOAD;

    @Value("${wflow.file.upload}")
    public void setFileUpload(String fileUpload) {
        FILE_UPLOAD = fileUpload;
    }

    /**
     * <h2>多文件上传</h2>
     * @param files:
     * @param fileTypeEnum:
     * @return java.util.List<com.tubond.uranos.model.vo.FileInfo>
     * <AUTHOR>
     * @date 2022-09-05 08:53
     */
    public static List<FileInfo> uploadFiles(MultipartFile[] files, FileTypeEnum fileTypeEnum) {
        if (null != files && files.length > 0) {
            List<FileInfo> fileInfos = new ArrayList<>();
            for (MultipartFile file : files) {
                FileInfo fileInfo = uploadFile(file, fileTypeEnum);
                fileInfos.add(fileInfo);
            }
            return fileInfos;
        }
        return null;
    }

    /**
     * 文件上传
     * @param file:
     * @param fileTypeEnum:
     * @return com.tubond.springbootmybatisplus.vo.FileInfo
     * <AUTHOR>
     * @date 2021-08-19 16:28
     */
    public static FileInfo uploadFile(MultipartFile file, FileTypeEnum fileTypeEnum) {
        // 生成目录
        createDir(fileTypeEnum);
        // 上传的文件信息
        FileInfo fileInfo = getFileInfo(file);
        File uploadFile = new File(fileInfo.getServerUrl());
        try {
            file.transferTo(uploadFile);

        } catch (IOException e) {
            log.error("上传文件失败：", e);
        }
        return fileInfo;
    }
    
    /**
     * 下载文件
     * @param url:
     * @return org.springframework.http.ResponseEntity<org.springframework.core.io.InputStreamResource>
     * <AUTHOR>
     * @date 2021-11-12 08:53
     */
    public static ResponseEntity<InputStreamResource> downloadFile(String url) throws IOException {
        FileSystemResource file = new FileSystemResource(FILE_UPLOAD + url);
        if (!file.exists()) {
            return ResponseEntity.notFound().build();
        }

        String fileName = file.getFilename();
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");

        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition",
                String.format("attachment; filename=\"%s\"; filename*=UTF-8''%s",
                        fileName, encodedFileName));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.contentLength())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(file.getInputStream()));
    }
    
    public static ResponseEntity<InputStreamResource> downloadFile(String fileName, InputStream in) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        //设置文件名并设置文件名编码，解决文件名异常问题
        headers.add("Content-Disposition", String.format("attachment; filename=\"%s\"", new
                String(fileName.getBytes("GBK"),"ISO8859-1")));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(new InputStreamResource(in));
    }

    /**
     * 获取文件信息
     * @param file:
     * @return com.tubond.springbootmybatisplus.vo.FileInfo
     * <AUTHOR>
     * @date 2021-08-19 16:23
     */
    private static FileInfo getFileInfo(MultipartFile file) {
        // 获取文件名字
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName.trim())) {
            log.error("文件名不正确！");
        }
        // 不带后缀的文件名
        String noSuffixFileName = fileName.substring(0, fileName.lastIndexOf("."));
        // 文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        // 新文件名
        String newFileName = SnowflakeUtil.snowflakeId() + "." + suffix;
        return FileInfo.builder()
                .fileName(fileName)
                .noSuffixFileName(noSuffixFileName)
                .newFileName(newFileName)
                .localUrl(LOCAL_URL + newFileName)
                .serverUrl(SERVER_URL + newFileName)
                .suffix(suffix)
                .build();
    }

    /**
     * 创建任意深度的文件所在文件夹,可以用来替代直接new File(path)。
     *
     * @param path
     * @return File对象
     */
    private static void buildDir(String path) {

        File file = new File(path);
        //寻找父目录是否存在
        //File parent = new File(file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(File.separator)));
        //如果父目录不存在，则递归寻找更上一层目录
        if (!file.exists()) {
            //buildDir(parent.getPath());
            //创建父目录
            file.mkdirs();
        }
        // return file;
    }


    /**
     * 生成日期文件夹
     * @param fileTypeEnum:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-08-19 15:55
     */
    private static void createDir(FileTypeEnum fileTypeEnum) {
        createDir(null, fileTypeEnum);
    }

    private static void createDir(MultipartFile file, FileTypeEnum fileTypeEnum) {
        execute(file, fileTypeEnum);
    }

    private static void execute(MultipartFile file, FileTypeEnum fileTypeEnum) {
        String ymd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 添加日期文件夹
        String datePath = FILE_UPLOAD + fileTypeEnum.getCode() + "/" + ymd + "/";
        if (null != file) {
            datePath = FILE_UPLOAD + fileTypeEnum.getCode() + "/" + ymd + "/" +
                    file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1) + "/";
        }
        // 设置本地路径 和服务器路径
        LOCAL_URL = fileTypeEnum.getCode() + "/" + ymd + "/";
        SERVER_URL = datePath;
        // 构建
        buildDir(datePath);
    }


}
