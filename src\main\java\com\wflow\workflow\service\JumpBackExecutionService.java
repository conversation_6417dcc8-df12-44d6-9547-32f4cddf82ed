package com.wflow.workflow.service;

import com.wflow.workflow.bean.jumpback.JumpBackResult;

import java.util.List;

/**
 * 流程回跳执行服务接口
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
public interface JumpBackExecutionService {

    /**
     * 检查是否满足回跳条件
     * @param processInstanceId 流程实例ID
     * @param nodeId 节点ID
     * @return 是否满足条件
     */
    boolean checkJumpBackCondition(String processInstanceId, String nodeId);

    /**
     * 执行回跳操作
     * @param processInstanceId 流程实例ID
     * @param configId 配置ID
     * @return 回跳结果
     */
    JumpBackResult executeJumpBack(String processInstanceId, String configId);

    /**
     * 执行回跳操作（带原因）
     * @param processInstanceId 流程实例ID
     * @param configId 配置ID
     * @param reason 回跳原因
     * @return 回跳结果
     */
    JumpBackResult executeJumpBack(String processInstanceId, String configId, String reason);

    /**
     * 获取回跳历史
     * @param processInstanceId 流程实例ID
     * @return 回跳历史列表
     */
    List<JumpBackResult> getJumpBackHistory(String processInstanceId);

    /**
     * 取消待执行的回跳
     * @param processInstanceId 流程实例ID
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean cancelPendingJumpBack(String processInstanceId, String configId);

    /**
     * 检查流程实例是否可以回跳
     * @param processInstanceId 流程实例ID
     * @param configId 配置ID
     * @return 是否可以回跳
     */
    boolean canJumpBack(String processInstanceId, String configId);

    /**
     * 获取当前回跳次数
     * @param processInstanceId 流程实例ID
     * @param configId 配置ID
     * @return 当前回跳次数
     */
    int getCurrentJumpCount(String processInstanceId, String configId);

    /**
     * 重置回跳状态
     * @param processInstanceId 流程实例ID
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean resetJumpBackState(String processInstanceId, String configId);
}
