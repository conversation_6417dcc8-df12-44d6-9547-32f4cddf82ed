package com.wflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.*;
import com.wflow.bean.enums.MatchTypeEnum;
import com.wflow.bean.vo.FormStatisticsVo;
import com.wflow.mapper.*;
import com.wflow.service.FormStatisticsService;
import com.wflow.service.OrgRepositoryService;
import com.wflow.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 表单统计服务实现类
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Service
public class FormStatisticsServiceImpl implements FormStatisticsService {

    @Autowired
    private WflowFormStatisticsConfigMapper statisticsConfigMapper;

    @Autowired
    private WflowFormStatisticsResultMapper statisticsResultMapper;

    @Autowired
    private WflowFormDataMapper formDataMapper;

    @Autowired
    private WflowModelsMapper modelsMapper;

    @Autowired
    private WflowProcessInstancesMapper processInstancesMapper;

    @Autowired
    private OrgRepositoryService orgRepositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Override
    @Transactional
    public String createStatisticsConfig(WflowFormStatisticsConfig config) {
        config.setId(IdUtil.objectId());
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        config.setCreateBy(UserUtil.getLoginUserId());
        config.setStatus(1);
        statisticsConfigMapper.insert(config);
        log.info("创建统计配置成功，配置ID: {}", config.getId());
        return config.getId();
    }

    @Override
    @Transactional
    public boolean updateStatisticsConfig(WflowFormStatisticsConfig config) {
        config.setUpdateTime(LocalDateTime.now());
        config.setUpdateBy(UserUtil.getLoginUserId());

        int result = statisticsConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新统计配置成功，配置ID: {}", config.getId());
            // 清除相关缓存
            clearStatisticsCache(config.getId());
        }
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteStatisticsConfig(String configId) {
        int result = statisticsConfigMapper.deleteById(configId);
        if (result > 0) {
            log.info("删除统计配置成功，配置ID: {}", configId);
            // 清除相关缓存
            clearStatisticsCache(configId);
        }
        return result > 0;
    }

    @Override
    @Transactional
    public boolean toggleStatisticsConfig(String configId, Integer status) {
        WflowFormStatisticsConfig config = new WflowFormStatisticsConfig();
        config.setId(configId);
        config.setStatus(status);
        config.setUpdateTime(LocalDateTime.now());
        config.setUpdateBy(UserUtil.getLoginUserId());

        int result = statisticsConfigMapper.updateById(config);
        if (result > 0) {
            log.info("切换统计配置状态成功，配置ID: {}, 状态: {}", configId, status);
            if (status == 0) {
                // 禁用时清除缓存
                clearStatisticsCache(configId);
            }
        }
        return result > 0;
    }

    @Override
    public Page<WflowFormStatisticsConfig> getStatisticsConfigs(Integer pageNo, Integer pageSize,
            String formId, Integer status) {
        Page<WflowFormStatisticsConfig> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<WflowFormStatisticsConfig> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(formId)) {
            wrapper.eq(WflowFormStatisticsConfig::getFormId, formId);
        }
        if (status != null) {
            wrapper.eq(WflowFormStatisticsConfig::getStatus, status);
        }

        wrapper.orderByDesc(WflowFormStatisticsConfig::getCreateTime);
        return statisticsConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<WflowFormStatisticsConfig> getStatisticsConfigsByFormId(String formId) {
        return statisticsConfigMapper.selectEnabledByFormId(formId);
    }

    @Override
    public FormStatisticsVo executeRealTimeStatistics(String configId, boolean includeFinished) {
        WflowFormStatisticsConfig config = statisticsConfigMapper.selectById(configId);
        if (config == null || config.getStatus() != 1) {
            throw new RuntimeException("统计配置不存在或已禁用");
        }

        return doStatistics(config, includeFinished);
    }

    @Override
    public List<FormStatisticsVo> executeBatchStatistics(List<String> configIds, boolean includeFinished) {
        List<FormStatisticsVo> results = new ArrayList<>();

        for (String configId : configIds) {
            try {
                FormStatisticsVo result = executeRealTimeStatistics(configId, includeFinished);
                results.add(result);
            } catch (Exception e) {
                log.error("执行统计失败，配置ID: {}", configId, e);
            }
        }

        return results;
    }

    @Override
    public List<FormStatisticsVo> executeStatisticsByFormId(String formId, boolean includeFinished) {
        List<WflowFormStatisticsConfig> configs = getStatisticsConfigsByFormId(formId);
        if (CollectionUtil.isEmpty(configs)) {
            return Collections.emptyList();
        }

        List<String> configIds = configs.stream()
                .map(WflowFormStatisticsConfig::getId)
                .collect(Collectors.toList());

        return executeBatchStatistics(configIds, includeFinished);
    }

    @Override
    public List<String> getFieldDistinctValues(String formId, String fieldId, Integer limit) {
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, formId)
                .eq(WflowFormData::getFieldId, fieldId)
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "")
                .groupBy(WflowFormData::getFieldValue)
                .orderByDesc(WflowFormData::getCreateTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<WflowFormData> dataList = formDataMapper.selectList(wrapper);
        return dataList.stream()
                .map(WflowFormData::getFieldValue)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean refreshStatisticsCache(String configId) {
        try {
            // 清除旧缓存
            clearStatisticsCache(configId);

            // 重新计算并缓存
            FormStatisticsVo result = executeRealTimeStatistics(configId, true);

            // 保存到缓存表
            saveStatisticsToCache(configId, result);

            log.info("刷新统计缓存成功，配置ID: {}", configId);
            return true;
        } catch (Exception e) {
            log.error("刷新统计缓存失败，配置ID: {}", configId, e);
            return false;
        }
    }
    /*
    * 查询菜单
    * */
    @Override
    public List<WflowFormStatisticsConfig> getMenu() {
        return  statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery().eq(WflowFormStatisticsConfig::getConfigName, "查询"));
    }

    /**
     * 清除统计缓存
     */
    private void clearStatisticsCache(String configId) {
        LambdaQueryWrapper<WflowFormStatisticsResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormStatisticsResult::getConfigId, configId);
        statisticsResultMapper.delete(wrapper);
    }

    /**
     * 保存统计结果到缓存
     */
    private void saveStatisticsToCache(String configId, FormStatisticsVo result) {
        for (FormStatisticsVo.StatisticsDetail detail : result.getDetails()) {
            WflowFormStatisticsResult cacheResult = WflowFormStatisticsResult.builder()
                    .id(IdUtil.objectId())
                    .configId(configId)
                    .formId(result.getFormId())
                    .fieldId(result.getFieldId())
                    .targetValue(detail.getTargetValue())
                    .matchCount(detail.getMatchCount())
                    .totalCount(result.getTotalInstanceCount())
                    .matchRate(detail.getMatchRate())
                    .lastUpdateTime(LocalDateTime.now())
                    .build();

            statisticsResultMapper.insert(cacheResult);
        }
    }

    /**
     * 执行统计逻辑
     */
    private FormStatisticsVo doStatistics(WflowFormStatisticsConfig config, boolean includeFinished) {
        // 解析目标值
        List<String> targetValues = parseTargetValues(config.getTargetValue());

        // 获取表单信息
        WflowModels model = modelsMapper.selectById(config.getFormId());
        String formName = model != null ? model.getFormName() : "未知表单";

        // 查询表单数据
        List<WflowFormData> formDataList = getFormDataByConfig(config, includeFinished);

        // 获取所有相关的流程实例
        Set<String> instanceIds = formDataList.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());

        Map<String, HistoricProcessInstance> instanceMap = getProcessInstanceMap(instanceIds);
        Map<String, String> userNameMap = getUserNameMap(instanceMap.values());

        // 执行匹配统计
        List<FormStatisticsVo.StatisticsDetail> details = new ArrayList<>();
        int totalMatchCount = 0;

        for (String targetValue : targetValues) {
            List<WflowFormData> matchedData = filterMatchedData(formDataList, targetValue, config.getMatchType());
            List<FormStatisticsVo.MatchedInstance> matchedInstances = buildMatchedInstances(
                    matchedData, instanceMap, userNameMap);

            int matchCount = matchedData.size();
            totalMatchCount += matchCount;

            BigDecimal matchRate = calculateMatchRate(matchCount, formDataList.size());

            FormStatisticsVo.StatisticsDetail detail = FormStatisticsVo.StatisticsDetail.builder()
                    .targetValue(targetValue)
                    .matchCount(matchCount)
                    .matchRate(matchRate)
                    .matchedInstances(matchedInstances)
                    .build();

            details.add(detail);
        }

        BigDecimal totalMatchRate = calculateMatchRate(totalMatchCount, formDataList.size());

        return FormStatisticsVo.builder()
                .configId(config.getId())
                .configName(config.getConfigName())
                .formId(config.getFormId())
                .formName(formName)
                .fieldId(config.getFieldId())
                .fieldName(config.getFieldName())
                .targetValues(targetValues)
                .matchType(config.getMatchType())
                .details(details)
                .totalMatchCount(totalMatchCount)
                .totalInstanceCount(formDataList.size())
                .totalMatchRate(totalMatchRate)
                .statisticsTime(LocalDateTime.now())
                .build();
    }

    /**
     * 解析目标值
     */
    private List<String> parseTargetValues(String targetValueJson) {
        try {
            if (targetValueJson.startsWith("[")) {
                // JSON数组格式
                JSONArray jsonArray = JSON.parseArray(targetValueJson);
                return jsonArray.toJavaList(String.class);
            } else {
                // 单个值
                return Collections.singletonList(targetValueJson);
            }
        } catch (Exception e) {
            log.warn("解析目标值失败，使用原始值: {}", targetValueJson, e);
            return Collections.singletonList(targetValueJson);
        }
    }

    /**
     * 根据配置查询表单数据
     */
    private List<WflowFormData> getFormDataByConfig(WflowFormStatisticsConfig config, boolean includeFinished) {
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, config.getFormId())
                .eq(WflowFormData::getFieldId, config.getFieldId())
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "");

        List<WflowFormData> allData = formDataMapper.selectList(wrapper);

        if (includeFinished) {
            return allData;
        }

        // 只包含运行中的流程
        Set<String> runningInstanceIds = getRunningInstanceIds();
        return allData.stream()
                .filter(data -> runningInstanceIds.contains(data.getInstanceId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取运行中的流程实例ID
     */
    private Set<String> getRunningInstanceIds() {
        List<ProcessInstance> runningInstances = runtimeService.createProcessInstanceQuery().list();
        return runningInstances.stream()
                .map(ProcessInstance::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取流程实例映射
     */
    private Map<String, HistoricProcessInstance> getProcessInstanceMap(Set<String> instanceIds) {
        if (CollectionUtil.isEmpty(instanceIds)) {
            return Collections.emptyMap();
        }

        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

        return instances.stream()
                .collect(Collectors.toMap(HistoricProcessInstance::getId, instance -> instance));
    }

    /**
     * 获取用户名映射
     */
    private Map<String, String> getUserNameMap(Collection<HistoricProcessInstance> instances) {
        Set<String> userIds = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        Map<String, String> userNameMap = new HashMap<>();
        for (String userId : userIds) {
            try {
                String userName = orgRepositoryService.getUserById(userId).getUserName();
                userNameMap.put(userId, userName);
            } catch (Exception e) {
                log.warn("获取用户名失败，用户ID: {}", userId, e);
                userNameMap.put(userId, "未知用户");
            }
        }

        return userNameMap;
    }

    /**
     * 过滤匹配的数据
     */
    private List<WflowFormData> filterMatchedData(List<WflowFormData> dataList, String targetValue, String matchType) {
        MatchTypeEnum matchTypeEnum = MatchTypeEnum.getByCode(matchType);

        return dataList.stream()
                .filter(data -> isValueMatched(data.getFieldValue(), targetValue, matchTypeEnum))
                .collect(Collectors.toList());
    }

    /**
     * 判断值是否匹配
     */
    private boolean isValueMatched(String fieldValue, String targetValue, MatchTypeEnum matchType) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }

        switch (matchType) {
            case EXACT:
            case EQUALS:
                return fieldValue.equals(targetValue);
            case CONTAINS:
                return fieldValue.contains(targetValue);
            case REGEX:
                try {
                    return Pattern.matches(targetValue, fieldValue);
                } catch (Exception e) {
                    log.warn("正则表达式匹配失败: {}", targetValue, e);
                    return false;
                }
            case GREATER_THAN:
                return compareNumeric(fieldValue, targetValue) > 0;
            case LESS_THAN:
                return compareNumeric(fieldValue, targetValue) < 0;
            case NOT_EQUALS:
                return !fieldValue.equals(targetValue);
            case RANGE:
                return isInRange(fieldValue, targetValue);
            default:
                return fieldValue.equals(targetValue);
        }
    }

    /**
     * 数值比较
     */
    private int compareNumeric(String value1, String value2) {
        try {
            BigDecimal num1 = new BigDecimal(value1);
            BigDecimal num2 = new BigDecimal(value2);
            return num1.compareTo(num2);
        } catch (NumberFormatException e) {
            // 如果不是数值，则按字符串比较
            return value1.compareTo(value2);
        }
    }

    /**
     * 判断是否在范围内
     */
    private boolean isInRange(String value, String rangeValue) {
        try {
            // 范围格式: "min,max" 或 "min-max"
            String[] parts = rangeValue.contains(",") ? rangeValue.split(",") : rangeValue.split("-");
            if (parts.length != 2) {
                return false;
            }

            BigDecimal num = new BigDecimal(value);
            BigDecimal min = new BigDecimal(parts[0].trim());
            BigDecimal max = new BigDecimal(parts[1].trim());

            return num.compareTo(min) >= 0 && num.compareTo(max) <= 0;
        } catch (Exception e) {
            log.warn("范围匹配失败: value={}, range={}", value, rangeValue, e);
            return false;
        }
    }

    /**
     * 构建匹配的实例列表
     */
    private List<FormStatisticsVo.MatchedInstance> buildMatchedInstances(
            List<WflowFormData> matchedData,
            Map<String, HistoricProcessInstance> instanceMap,
            Map<String, String> userNameMap) {

        return matchedData.stream().<FormStatisticsVo.MatchedInstance>map(data -> {
            HistoricProcessInstance instance = instanceMap.get(data.getInstanceId());
            if (instance == null) {
                return null;
            }

            String startUserId = instance.getStartUserId();
            String startUserName = userNameMap.getOrDefault(startUserId, "未知用户");

            LocalDateTime createTime = null;
            if (data.getCreateTime() != null) {
                createTime = data.getCreateTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
            }

            return FormStatisticsVo.MatchedInstance.builder()
                    .instanceId(data.getInstanceId())
                    .instanceName(instance.getName())
                    .startUser(startUserId)
                    .startUserName(startUserName)
                    .fieldValue(data.getFieldValue())
                    .createTime(createTime)
                    .status(instance.getEndTime() != null ? "已完成" : "运行中")
                    .build();
        })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 计算匹配率
     */
    private BigDecimal calculateMatchRate(int matchCount, int totalCount) {
        if (totalCount == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(matchCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);
    }
}
